"""
STRICT AGENT INSTRUCTIONS FOR CODE GENERATION
===========================================

This file contains the strict instructions that should be applied to all agents
to prevent conversation drift and ensure they return only code blocks during
code generation phases.

PROBLEM ANALYSIS:
1. Agents start discussing instead of returning code
2. Conversations drift into "random fluff" as they progress
3. Some agents return explanations instead of pure code blocks
4. The multi-agent system bypasses agents entirely in some phases

SOLUTION:
1. Update all agent system messages with CRITICAL CODE-ONLY INSTRUCTIONS
2. Modify conversation flow to enforce single-turn code generation
3. Add code extraction utilities to handle any non-code responses
4. Implement fallback mechanisms to prevent system failures
"""

# STRICT SYSTEM MESSAGE TEMPLATE FOR CODE GENERATION AGENTS
STRICT_CODE_GENERATION_INSTRUCTIONS = """
CRITICAL INSTRUCTIONS FOR CODE GENERATION:
==========================================

ABSOLUTE REQUIREMENTS:
- You MUST return ONLY complete, working Python code blocks
- NO explanations, NO discussions, NO markdown text outside code blocks
- Your response must be ONLY the requested code, nothing else
- Use triple backticks with 'python' language identifier
- Include ALL necessary imports and complete function definitions
- Code must be production-ready and executable
- NO conversational responses or acknowledgments

RESPONSE FORMAT ENFORCEMENT:
Your response must follow this EXACT format:
```python
# Complete working code here
# Include all imports
# Include all function definitions
# Include error handling
# Include logging setup
```

FORBIDDEN RESPONSES:
- "I'll help you with that..."
- "Here's the code you requested..."
- "Let me create..."
- Any explanatory text before or after code blocks
- Multiple code blocks with explanations between them
- Incomplete code requiring "fill in the blanks"

REQUIRED CODE QUALITY:
- Complete, runnable Python code
- All necessary imports included
- Proper error handling
- Logging configuration
- Production-ready structure
- No placeholder comments like "# Add your code here"

SINGLE FOCUS:
Generate ONLY the specific component requested. Do not include:
- Multiple unrelated components
- Example usage code
- Test code
- Documentation beyond minimal comments
"""

# ENHANCED AGENT CONVERSATION UTILITIES
def create_strict_code_prompt(component_type: str, requirements: dict) -> str:
    """Create a strict prompt that enforces code-only responses."""
    
    base_prompt = f"""
{STRICT_CODE_GENERATION_INSTRUCTIONS}

COMPONENT: {component_type}
REQUIREMENTS: {requirements}

Generate the complete Python code for this component.
Return ONLY the code block, nothing else.
"""
    return base_prompt

def extract_code_from_response(response: str) -> str:
    """Extract Python code from agent response with multiple fallback strategies."""
    import re
    
    # Strategy 1: Find code blocks with python identifier
    python_blocks = re.findall(r'```python\n(.*?)\n```', response, re.DOTALL)
    if python_blocks:
        return python_blocks[0].strip()
    
    # Strategy 2: Find any code blocks
    code_blocks = re.findall(r'```\n(.*?)\n```', response, re.DOTALL)
    if code_blocks:
        return code_blocks[0].strip()
    
    # Strategy 3: Find code blocks without newlines
    code_blocks_inline = re.findall(r'```python(.*?)```', response, re.DOTALL)
    if code_blocks_inline:
        return code_blocks_inline[0].strip()
    
    # Strategy 4: Look for import statements and assume rest is code
    if 'import ' in response:
        lines = response.split('\n')
        code_lines = []
        in_code = False
        for line in lines:
            if 'import ' in line or 'from ' in line:
                in_code = True
            if in_code and not line.strip().startswith('#') and not line.strip().startswith('*'):
                code_lines.append(line)
        if code_lines:
            return '\n'.join(code_lines).strip()
    
    # Strategy 5: Return the whole response if it looks like code
    if any(keyword in response for keyword in ['def ', 'class ', 'import ', 'from ']):
        return response.strip()
    
    # Strategy 6: Last resort - return empty code template
    return """
# Code generation failed - using fallback template
import logging

logger = logging.getLogger(__name__)

def placeholder_function():
    logger.warning("This is a placeholder function - code generation failed")
    pass
"""

def execute_single_turn_code_generation(agent, user_proxy, prompt: str) -> str:
    """Execute a single-turn conversation focused on code generation."""
    
    try:
        # Execute with strict single turn
        result = user_proxy.initiate_chat(
            agent,
            message=prompt,
            max_turns=1,  # Enforce single turn to prevent drift
            silent=True   # Reduce noise
        )
        
        # Extract response
        if hasattr(result, 'chat_history') and result.chat_history:
            response = result.chat_history[-1]["content"]
        else:
            response = str(result)
        
        # Extract and validate code
        code = extract_code_from_response(response)
        
        # Validate that we got actual code
        if not any(keyword in code for keyword in ['def ', 'class ', 'import ', 'from ']):
            raise ValueError("Agent did not return valid Python code")
        
        return code
        
    except Exception as e:
        # Log the error and return a fallback
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Code generation failed: {e}")
        
        return f"""
# Code generation failed: {str(e)}
# Using fallback implementation
import logging

logger = logging.getLogger(__name__)

def fallback_implementation():
    logger.error("Code generation failed - using fallback")
    raise NotImplementedError("Code generation failed")
"""

# AGENT SYSTEM MESSAGE UPDATES
def get_updated_agent_system_messages():
    """Get updated system messages for all agents with strict code instructions."""
    
    return {
        "data_loader": f"""You are a Data Loading Agent.

{STRICT_CODE_GENERATION_INSTRUCTIONS}

Your role is to:
1. Select appropriate PDF parsing strategies (Unstructured, LlamaParse, PyMuPDF4LLM, Docling, VLM)
2. Select web scraping approaches (RecursiveUrlLoader)
3. Generate complete, production-ready data loading code
4. Ensure proper Prefect flow integration

Available PDF parsing options:
- Unstructured: Good for general documents, supports images
- LlamaParse: Premium parsing with advanced layout understanding
- PyMuPDF4LLM: Fast and efficient for text-heavy documents
- Docling: Advanced document understanding with OCR
- VLM: Vision Language Model based parsing for complex layouts

Available web scraping:
- RecursiveUrlLoader: For crawling websites with depth control""",

        "chunker": f"""You are a Chunking Strategy Agent.

{STRICT_CODE_GENERATION_INSTRUCTIONS}

Your role is to:
1. Select appropriate chunking strategies from Data_Eng_Database/chunking/
2. Generate complete, production-ready chunking code
3. Ensure compatibility with the chosen embedding model and use case

Available chunking strategies:
- Fixed-Size Chunking: Simple, consistent chunk sizes with overlap
- Recursive Chunking: Hierarchical splitting with specialized splitters
- Semantic Chunking: Groups semantically related content
- Smart/Adaptive Chunking: Adapts to document structure
- Sliding-Window Chunking: Overlapping windows for context preservation""",

        "embedder": f"""You are an Embedding Agent.

{STRICT_CODE_GENERATION_INSTRUCTIONS}

Your role is to:
1. Select appropriate embedding models from Data_Eng_Database/embeddings/
2. Generate complete, production-ready embedding code
3. Ensure compatibility with the chosen vector database

Available embedding options:
- OpenAI Embeddings: High quality, good for general use
- Cohere Embeddings: Good for multilingual and specialized domains
- Jina Embeddings: Optimized for various tasks
- Gemini Embeddings: Google's embedding models
- Azure OpenAI Embeddings: Enterprise-grade OpenAI embeddings""",

        "vector_store": f"""You are a Vector Store Agent.

{STRICT_CODE_GENERATION_INSTRUCTIONS}

Your role is to:
1. Select appropriate vector databases from Data_Eng_Database/vector_stores/
2. Generate complete, production-ready vector store code
3. Ensure compatibility with chosen embedding dimensions

Available vector stores:
- Pinecone: Managed vector database, good for production
- Qdrant: Open source, good for self-hosting
- Weaviate: Feature-rich with built-in vectorization
- Chroma: Simple and lightweight for development""",

        "llm": f"""You are an LLM Agent.

{STRICT_CODE_GENERATION_INSTRUCTIONS}

Your role is to:
1. Select appropriate language models from Data_Eng_Database/llms/
2. Generate complete, production-ready LLM integration code
3. Set up RAG (Retrieval Augmented Generation) workflows

Available LLM options:
- Azure OpenAI: Enterprise-grade GPT models
- AWS Bedrock: Various models (Claude, Llama, etc.)
- Groq: Fast inference for supported models
- Local models: For privacy-sensitive applications""",

        "tools": f"""You are a Tools Agent.

{STRICT_CODE_GENERATION_INSTRUCTIONS}

Your role is to:
1. Select and configure external tools from Data_Eng_Database/tools/
2. Generate complete, production-ready tools integration code
3. Configure API connections and parameters

Available tools:
- Tavily: Web search and content extraction
- Serper: Google Search API integration
- Slack: Team communication integration
- Jira: Project management integration"""
    }

# CONVERSATION FLOW IMPROVEMENTS
class StrictCodeGenerationManager:
    """Manager for strict code generation with fallback mechanisms."""
    
    def __init__(self, agents, user_proxy):
        self.agents = agents
        self.user_proxy = user_proxy
        self.fallback_enabled = True
        
    def generate_component_code(self, component_type: str, requirements: dict, fallback_selector=None) -> str:
        """Generate code for a specific component with strict controls."""
        
        # Create strict prompt
        prompt = create_strict_code_prompt(component_type, requirements)
        
        # Get appropriate agent
        agent_map = {
            "data_loading": "data_loader",
            "chunking": "chunker", 
            "embedding": "embedder",
            "vector_store": "vector_store",
            "llm": "llm",
            "tools": "tools"
        }
        
        agent_name = agent_map.get(component_type)
        if not agent_name or agent_name not in self.agents:
            raise ValueError(f"No agent available for component type: {component_type}")
        
        agent = self.agents[agent_name]
        
        try:
            # Execute strict single-turn code generation
            code = execute_single_turn_code_generation(agent, self.user_proxy, prompt)
            return code
            
        except Exception as e:
            if self.fallback_enabled and fallback_selector:
                # Use fallback code selector
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Agent code generation failed for {component_type}, using fallback: {e}")
                
                # Use fallback based on component type
                if component_type == "data_loading" and "pdf_parsing_strategy" in requirements:
                    return fallback_selector.get_pdf_parsing_code(requirements["pdf_parsing_strategy"])
                elif component_type == "chunking" and "chunking_strategy" in requirements:
                    return fallback_selector.get_chunking_code(requirements["chunking_strategy"])
                elif component_type == "embedding" and "embedding_provider" in requirements:
                    return fallback_selector.get_embedding_code(requirements["embedding_provider"])
                elif component_type == "vector_store" and "vector_store" in requirements:
                    return fallback_selector.get_vector_store_code(requirements["vector_store"])
                elif component_type == "llm" and "llm_provider" in requirements:
                    return fallback_selector.get_llm_code(requirements["llm_provider"])
                elif component_type == "tools" and "tool_type" in requirements:
                    return fallback_selector.get_tools_code(requirements["tool_type"])
            
            # Re-raise if no fallback available
            raise
